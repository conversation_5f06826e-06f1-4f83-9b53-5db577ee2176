<!DOCTYPE html>
<html lang="ar" dir="rtl">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>عارض جهات الاتصال (VCF)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/qrcode/build/qrcode.min.js"></script>
    <style>
        body {
            font-family: 'Segoe UI', sans-serif;
            transition: background 0.5s, color 0.5s;
        }

        .glass {
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.18);
            backdrop-filter: blur(16px) saturate(180%);
            -webkit-backdrop-filter: blur(16px) saturate(180%);
            border-radius: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.25);
            transition: background 0.3s, box-shadow 0.3s;
        }

        .glass input,
        .glass button,
        .glass select {
            background: rgba(255, 255, 255, 0.6) !important;
            color: #222 !important;
            border: none;
            box-shadow: 0 2px 8px 0 rgba(31, 38, 135, 0.08);
        }

        .glass input:focus {
            outline: 2px solid #3b82f6;
            background: rgba(255, 255, 255, 0.85) !important;
        }

        .glass button:hover {
            background: rgba(59, 130, 246, 0.15) !important;
        }

        .dark-mode {
            background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
            color: white;
        }

        .light-mode {
            background: linear-gradient(135deg, #f9fafb 0%, #e5e7eb 100%);
            color: #111827;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem;
            border-radius: 0.5rem;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: #10b981;
        }

        .notification.error {
            background: #ef4444;
        }

        .notification.info {
            background: #3b82f6;
        }

        input[type="file"]::file-selector-button {
            background: #3b82f6;
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 0.5rem;
            border: none;
        }
    </style>
</head>

<body class="dark-mode min-h-screen flex flex-col items-center p-4" id="app">
    <h1 class="text-3xl font-bold mb-4">عارض جهات الاتصال من ملف VCF</h1>

    <div class="mb-4 flex flex-wrap items-center gap-2 glass shadow-lg p-4 w-full max-w-6xl justify-center">
        <label class="cursor-pointer bg-blue-500/80 hover:bg-blue-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">
            📤 اختر ملف VCF
            <input type="file" id="vcfInput" accept=".vcf" hidden />
        </label>
        <label class="cursor-pointer bg-indigo-500/80 hover:bg-indigo-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">
            ➕ دمج ملف إضافي
            <input type="file" id="mergeInput" accept=".vcf" hidden />
        </label>
        <button id="exportBtn" class="bg-green-500/80 hover:bg-green-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">⬇️ تصدير إلى CSV</button>
        <button id="exportVCFBtn" class="bg-pink-500/80 hover:bg-pink-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">💾 تصدير إلى VCF</button>
        <button id="toggleMode" class="bg-yellow-500/80 hover:bg-yellow-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">🌙 تبديل الوضع</button>
        <button id="toggleView" class="bg-purple-500/80 hover:bg-purple-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">🔄 تغيير العرض</button>
        <button id="fixEncoding" class="bg-red-500/80 hover:bg-red-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">🔧 إصلاح الترميز</button>
        <button id="findDuplicates" class="bg-orange-500/80 hover:bg-orange-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">🔍 الأرقام المتكررة</button>
        <button id="clearAll" class="bg-gray-500/80 hover:bg-gray-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">🗑️ مسح الكل</button>
        <button id="shareAll" class="bg-teal-500/80 hover:bg-teal-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">📤 مشاركة الكل</button>
        <!-- زر إحصائيات متقدمة -->
        <button id="advStatsBtn" class="bg-cyan-500/80 hover:bg-cyan-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">📊 إحصائيات</button>
        <!-- زر طباعة -->
        <button id="printBtn" class="bg-gray-700/80 hover:bg-gray-800/90 px-3 py-2 rounded text-white text-sm shadow transition-all">🖨️ طباعة</button>
        <!-- زر نسخ كل الأرقام -->
        <button id="copyPhonesBtn" class="bg-lime-500/80 hover:bg-lime-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all">📋 نسخ كل الأرقام</button>
    </div>

    <input type="text" id="searchInput" placeholder="🔍 ابحث باسم أو رقم..."
        class="glass text-black/90 p-2 rounded mb-4 w-full max-w-md shadow focus:outline-none focus:ring-2 focus:ring-blue-400 transition-all bg-white/60 placeholder:text-gray-500" />

    <div id="statsContainer" class="mb-4 text-center glass p-2 shadow w-full max-w-6xl flex flex-wrap gap-2 justify-center">
        <span id="contactCount" class="bg-blue-600/80 text-white px-3 py-1 rounded-full text-sm shadow">0 جهة اتصال</span>
        <span id="filteredCount" class="bg-green-600/80 text-white px-3 py-1 rounded-full text-sm ml-2 hidden shadow">0 نتيجة</span>
    </div>

    <div id="contactsContainer" class="grid gap-4 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 w-full max-w-6xl"></div>

    <div id="qrModal"
        class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-70 hidden items-center justify-center z-50">
        <div class="bg-white text-black p-6 rounded-lg shadow-xl max-w-sm">
            <h3 class="text-lg font-bold mb-4 text-center">مشاركة جهة الاتصال</h3>
            <div id="qrCode" class="flex justify-center mb-4"></div>
            <div class="flex gap-2 justify-center">
                <button id="downloadQR" class="px-4 py-2 bg-blue-500 text-white rounded">⬇️ تحميل</button>
                <button id="shareQR" class="px-4 py-2 bg-green-500 text-white rounded">📤 مشاركة</button>
                <button onclick="document.getElementById('qrModal').classList.add('hidden')"
                    class="px-4 py-2 bg-red-500 text-white rounded">إغلاق</button>
            </div>
        </div>
    </div>

    <!-- Modal للأرقام المتكررة -->
    <div id="duplicatesModal"
        class="fixed top-0 left-0 w-full h-full bg-black bg-opacity-70 hidden items-center justify-center z-50">
        <div class="bg-white text-black p-6 rounded-lg shadow-xl max-w-4xl max-h-96 overflow-auto">
            <h3 class="text-lg font-bold mb-4 text-center">🔍 الأرقام المتكررة</h3>
            <div id="duplicatesList" class="mb-4"></div>
            <div class="flex gap-2 justify-center">
                <button id="removeDuplicates" class="px-4 py-2 bg-red-500 text-white rounded">🗑️ حذف المتكررات</button>
                <button onclick="document.getElementById('duplicatesModal').classList.add('hidden')"
                    class="px-4 py-2 bg-gray-500 text-white rounded">إغلاق</button>
            </div>
        </div>
    </div>

    <script>
        const app = document.getElementById('app');
        const vcfInput = document.getElementById('vcfInput');
        const mergeInput = document.getElementById('mergeInput');
        const searchInput = document.getElementById('searchInput');
        const exportBtn = document.getElementById('exportBtn');
        const exportVCFBtn = document.getElementById('exportVCFBtn');
        const toggleMode = document.getElementById('toggleMode');
        const toggleView = document.getElementById('toggleView');
        const fixEncoding = document.getElementById('fixEncoding');
        const findDuplicates = document.getElementById('findDuplicates');
        const clearAll = document.getElementById('clearAll');
        const shareAll = document.getElementById('shareAll');
        const contactsContainer = document.getElementById('contactsContainer');
        const qrModal = document.getElementById('qrModal');
        const qrCodeContainer = document.getElementById('qrCode');
        const duplicatesModal = document.getElementById('duplicatesModal');
        const duplicatesList = document.getElementById('duplicatesList');
        const removeDuplicates = document.getElementById('removeDuplicates');

        let allContacts = [];
        let viewMode = 'cards';
        let dark = true;
        let lastFileContent = '';
        let duplicatePhones = [];

        // وظيفة عرض الإشعارات
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);

            setTimeout(() => notification.classList.add('show'), 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 300);
            }, 3000);
        }

        // وظيفة نسخ النص إلى الحافظة
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(() => {
                showNotification(`تم نسخ: ${text}`, 'success');
            }).catch(() => {
                showNotification('فشل في نسخ النص', 'error');
            });
        }

        // وظيفة تحديث الإحصائيات
        function updateStats(displayedCount) {
            const contactCount = document.getElementById('contactCount');
            const filteredCount = document.getElementById('filteredCount');

            contactCount.textContent = `${allContacts.length} جهة اتصال`;

            if (displayedCount < allContacts.length) {
                filteredCount.textContent = `${displayedCount} نتيجة`;
                filteredCount.classList.remove('hidden');
            } else {
                filteredCount.classList.add('hidden');
            }
        }

        vcfInput.addEventListener('change', handleFile);
        mergeInput.addEventListener('change', handleMergeFile);
        searchInput.addEventListener('input', filterContacts);
        exportBtn.addEventListener('click', exportToCSV);
        exportVCFBtn.addEventListener('click', exportToVCF);
        toggleMode.addEventListener('click', () => {
            dark = !dark;
            app.className = dark ? 'dark-mode min-h-screen flex flex-col items-center p-4' : 'light-mode min-h-screen flex flex-col items-center p-4';
        });
        toggleView.addEventListener('click', () => {
            viewMode = viewMode === 'cards' ? 'table' : 'cards';
            displayContacts(allContacts);
        });
        fixEncoding.addEventListener('click', () => {
            if (lastFileContent) {
                fixEncodingIssues();
            } else {
                showNotification('يرجى تحميل ملف VCF أولاً', 'error');
            }
        });
        findDuplicates.addEventListener('click', findDuplicateNumbers);
        clearAll.addEventListener('click', clearAllContacts);
        shareAll.addEventListener('click', shareAllContacts);
        removeDuplicates.addEventListener('click', removeDuplicateContacts);

        // خصائص جديدة لتطوير البرنامج:
        // 1. إحصائيات متقدمة
        function showAdvancedStats() {
            const total = allContacts.length;
            const withPhone = allContacts.filter(c => c.phone && c.phone.length).length;
            const withEmail = allContacts.filter(c => c.email).length;
            const uniquePhones = new Set(allContacts.flatMap(c => c.phone || [])).size;
            showNotification(`إجمالي: ${total} | بأرقام: ${withPhone} | ببريد: ${withEmail} | أرقام فريدة: ${uniquePhones}`, 'info');
        }
        // زر إحصائيات متقدمة
        const advStatsBtn = document.createElement('button');
        advStatsBtn.textContent = '📊 إحصائيات';
        advStatsBtn.className = 'bg-cyan-500/80 hover:bg-cyan-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all';
        advStatsBtn.onclick = showAdvancedStats;
        document.querySelector('.mb-4.flex').appendChild(advStatsBtn);

        // 2. خاصية طباعة جهات الاتصال
        function printContacts() {
            const win = window.open('', '', 'width=900,height=700');
            win.document.write('<html><head><title>طباعة جهات الاتصال</title><style>body{font-family:Segoe UI,sans-serif;direction:rtl;}table{width:100%;border-collapse:collapse;}th,td{border:1px solid #ccc;padding:8px;}th{background:#f3f4f6;}</style></head><body>');
            win.document.write('<h2>جهات الاتصال</h2>');
            win.document.write('<table><thead><tr><th>الاسم</th><th>الهاتف</th><th>البريد</th><th>الشركة</th></tr></thead><tbody>');
            allContacts.forEach(c => {
                win.document.write(`<tr><td>${c.name||''}</td><td>${(c.phone||[]).join('<br>')}</td><td>${c.email||''}</td><td>${c.org||''}</td></tr>`);
            });
            win.document.write('</tbody></table></body></html>');
            win.document.close();
            win.print();
        }
        // زر طباعة
        const printBtn = document.createElement('button');
        printBtn.textContent = '🖨️ طباعة';
        printBtn.className = 'bg-gray-700/80 hover:bg-gray-800/90 px-3 py-2 rounded text-white text-sm shadow transition-all';
        printBtn.onclick = printContacts;
        document.querySelector('.mb-4.flex').appendChild(printBtn);

        // 3. خاصية نسخ كل الأرقام
        function copyAllPhones() {
            const allPhones = allContacts.flatMap(c => c.phone || []).filter(Boolean).join('\n');
            copyToClipboard(allPhones);
        }
        // زر نسخ كل الأرقام
        const copyPhonesBtn = document.createElement('button');
        copyPhonesBtn.textContent = '📋 نسخ كل الأرقام';
        copyPhonesBtn.className = 'bg-lime-500/80 hover:bg-lime-600/90 px-3 py-2 rounded text-white text-sm shadow transition-all';
        copyPhonesBtn.onclick = copyAllPhones;
        document.querySelector('.mb-4.flex').appendChild(copyPhonesBtn);

        function decodeUTF8(input) {
            if (!input) return input;

            try {
                // إذا كان النص يحتوي على ترميز URL مثل %D8%A7
                if (input.includes('%')) {
                    return decodeURIComponent(input);
                }

                // إذا كان النص يحتوي على ترميز quoted-printable مثل =D8=A7
                if (input.includes('=') && /=[0-9A-F]{2}/i.test(input)) {
                    // تحويل من quoted-printable إلى UTF-8
                    let decoded = input.replace(/=([0-9A-F]{2})/gi, (match, hex) => {
                        return String.fromCharCode(parseInt(hex, 16));
                    });

                    // محاولة فك ترميز UTF-8
                    try {
                        return new TextDecoder('utf-8').decode(
                            new Uint8Array([...decoded].map(char => char.charCodeAt(0)))
                        );
                    } catch {
                        return decoded;
                    }
                }

                // إذا كان النص يحتوي على ترميز base64
                if (/^[A-Za-z0-9+/]+=*$/.test(input) && input.length % 4 === 0) {
                    try {
                        return atob(input);
                    } catch {
                        return input;
                    }
                }

                // محاولة فك ترميز عادي
                return decodeURIComponent(escape(input));
            } catch (e) {
                console.log('خطأ في فك الترميز:', e, 'للنص:', input);
                return input;
            }
        }

        function handleFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                let text = e.target.result;
                lastFileContent = text; // حفظ المحتوى الأصلي

                // محاولة اكتشاف الترميز وإصلاحه
                if (text.includes('=D8=') || text.includes('=D9=')) {
                    console.log('تم اكتشاف ترميز quoted-printable');
                }

                allContacts = parseVCF(text);
                displayContacts(allContacts);
                localStorage.setItem('lastVCF', text);
                showNotification(`تم تحميل ${allContacts.length} جهة اتصال بنجاح`, 'success');
            };

            // محاولة قراءة الملف بترميزات مختلفة
            reader.readAsText(file, 'utf-8');
        }

        function handleMergeFile(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = (e) => {
                let text = e.target.result;

                // تحليل الملف الجديد
                const newContacts = parseVCF(text);

                if (newContacts.length === 0) {
                    showNotification('لم يتم العثور على جهات اتصال في الملف', 'error');
                    return;
                }

                // دمج جهات الاتصال الجديدة مع الموجودة
                const beforeCount = allContacts.length;
                allContacts = [...allContacts, ...newContacts];

                displayContacts(allContacts);
                showNotification(`تم دمج ${newContacts.length} جهة اتصال جديدة. المجموع: ${allContacts.length}`, 'success');

                // تحديث localStorage
                const combinedVCF = lastFileContent + '\n' + text;
                localStorage.setItem('lastVCF', combinedVCF);
                lastFileContent = combinedVCF;
            };

            reader.readAsText(file, 'utf-8');
        }

        function fixEncodingIssues() {
            if (!lastFileContent) return;

            // محاولة إصلاح الترميز بطرق مختلفة
            let fixedContent = lastFileContent;

            // إصلاح quoted-printable encoding
            fixedContent = fixedContent.replace(/=([0-9A-F]{2})/gi, (match, hex) => {
                return String.fromCharCode(parseInt(hex, 16));
            });

            // محاولة فك ترميز UTF-8
            try {
                const bytes = new Uint8Array([...fixedContent].map(char => char.charCodeAt(0)));
                fixedContent = new TextDecoder('utf-8').decode(bytes);
            } catch (e) {
                console.log('فشل في فك ترميز UTF-8:', e);
            }

            // إعادة تحليل المحتوى المُصحح
            const newContacts = parseVCF(fixedContent);
            if (newContacts.length > allContacts.length) {
                allContacts = newContacts;
                displayContacts(allContacts);
                showNotification('تم إصلاح الترميز بنجاح!', 'success');
            } else {
                showNotification('لم يتم العثور على تحسينات في الترميز', 'info');
            }
        }

        function parseVCF(text) {
            // دمج السطور الملتفة (line folding)
            text = text.replace(/\r?\n[ \t]/g, '');
            const cards = text.split('BEGIN:VCARD').slice(1);
            return cards.map(card => {
                const lines = card.split('\n');
                const contact = {};

                lines.forEach(line => {
                    line = line.trim();
                    if (line.includes(':')) {
                        const [key, ...valueParts] = line.split(':');
                        const valueRaw = valueParts.join(':');
                        let value = valueRaw;
                        // معالجة الاسماء والارقام مع دعم CHARSET وENCODING
                        if (/FN|N:/.test(key)) {
                            if (/ENCODING=QUOTED-PRINTABLE/i.test(key)) {
                                if (/CHARSET=UTF-8/i.test(key)) {
                                    let qp = value.replace(/=([0-9A-F]{2})/gi, (match, hex) => String.fromCharCode(parseInt(hex, 16)));
                                    try {
                                        value = new TextDecoder('utf-8').decode(new Uint8Array([...qp].map(c => c.charCodeAt(0))));
                                    } catch {
                                        value = qp;
                                    }
                                } else if (/CHARSET=WINDOWS-1256/i.test(key)) {
                                    let qp = value.replace(/=([0-9A-F]{2})/gi, (match, hex) => String.fromCharCode(parseInt(hex, 16)));
                                    try {
                                        value = new TextDecoder('windows-1256').decode(new Uint8Array([...qp].map(c => c.charCodeAt(0))));
                                    } catch {
                                        value = qp;
                                    }
                                } else {
                                    value = value.replace(/=([0-9A-F]{2})/gi, (match, hex) => String.fromCharCode(parseInt(hex, 16)));
                                }
                            } else if (/ENCODING=BASE64|ENCODING=B/i.test(key)) {
                                try {
                                    value = atob(value);
                                } catch {}
                            }
                        } else if (/TEL/.test(key)) {
                            if (/ENCODING=QUOTED-PRINTABLE/i.test(key)) {
                                let qp = value.replace(/=([0-9A-F]{2})/gi, (match, hex) => String.fromCharCode(parseInt(hex, 16)));
                                try {
                                    value = new TextDecoder('utf-8').decode(new Uint8Array([...qp].map(c => c.charCodeAt(0))));
                                } catch {
                                    value = qp;
                                }
                            } else if (/ENCODING=BASE64|ENCODING=B/i.test(key)) {
                                try {
                                    value = atob(value);
                                } catch {}
                            }
                            // تنظيف الرقم من أي رموز غير رقمية (عدا + في البداية)
                            value = value.replace(/[^\d+]/g, (c, i) => (i === 0 && c === '+') ? '+' : '');
                        } else {
                            value = decodeUTF8(valueRaw);
                        }
                        if (value) {
                            value = value.replace(/\r/g, '').trim();
                        }
                        if (/FN|N:/.test(key)) {
                            contact.name = value;
                        }
                        if (/TEL/.test(key)) {
                            contact.phone = contact.phone || [];
                            if (value) contact.phone.push(value);
                        }
                        if (/EMAIL/.test(key)) contact.email = value;
                        if (/ADR/.test(key)) contact.address = value.replace(/;/g, ' ');
                        if (/NOTE/.test(key)) contact.note = value;
                        if (/ORG/.test(key)) contact.org = value;
                        if (/BDAY/.test(key)) contact.birthday = value;
                        if (/URL/.test(key)) contact.url = value;
                    }
                });
                // ترتيب أرقام الهاتف تصاعدياً (حسب القيمة الرقمية)
                if (contact.phone && contact.phone.length > 1) {
                    contact.phone = contact.phone.filter(Boolean).sort((a, b) => {
                        const na = a.replace(/\D/g, '');
                        const nb = b.replace(/\D/g, '');
                        return na.localeCompare(nb, 'en', {numeric: true});
                    });
                }
                // تحسين: إزالة الأرقام المكررة داخل نفس جهة الاتصال
                if (contact.phone) {
                    contact.phone = [...new Set(contact.phone)];
                }
                return contact;
            }).filter(contact => contact.name || contact.phone);
        }

        // تحسين: إضافة زر "حفظ كـ VCF" لتصدير جميع جهات الاتصال كملف VCF
        function exportToVCF() {
            if (allContacts.length === 0) {
                showNotification('لا يوجد جهات اتصال لتصديرها', 'error');
                return;
            }
            let vcf = allContacts.map(c => {
                let v = 'BEGIN:VCARD\nVERSION:3.0\n';
                if (c.name) v += `FN:${c.name}\n`;
                if (c.phone) c.phone.forEach(p => v += `TEL;TYPE=CELL:${p}\n`);
                if (c.email) v += `EMAIL:${c.email}\n`;
                if (c.address) v += `ADR:${c.address.replace(/\n/g, ';')}\n`;
                if (c.org) v += `ORG:${c.org}\n`;
                if (c.birthday) v += `BDAY:${c.birthday}\n`;
                if (c.url) v += `URL:${c.url}\n`;
                if (c.note) v += `NOTE:${c.note}\n`;
                v += 'END:VCARD\n';
                return v;
            }).join('');
            const blob = new Blob([vcf], { type: 'text/vcard;charset=utf-8;' });
            const link = document.createElement('a');
            link.href = URL.createObjectURL(blob);
            link.download = 'contacts.vcf';
            link.click();
            showNotification(`تم تصدير ${allContacts.length} جهة اتصال إلى VCF`, 'success');
        }

        let currentQRCanvas = null;
        let currentQRData = '';

        function showQR(data) {
            currentQRData = decodeURIComponent(data);
            qrModal.classList.remove('hidden');
            qrCodeContainer.innerHTML = '';

            QRCode.toCanvas(document.createElement('canvas'), currentQRData, {
                width: 200,
                margin: 2,
                color: {
                    dark: '#000000',
                    light: '#FFFFFF'
                }
            }, (err, canvas) => {
                if (!err) {
                    currentQRCanvas = canvas;
                    qrCodeContainer.appendChild(canvas);

                    // إضافة event listeners لأزرار التحميل والمشاركة
                    document.getElementById('downloadQR').onclick = downloadQR;
                    document.getElementById('shareQR').onclick = shareQR;
                }
            });
        }

        function downloadQR() {
            if (!currentQRCanvas) return;

            const link = document.createElement('a');
            link.download = 'contact-qr.png';
            link.href = currentQRCanvas.toDataURL();
            link.click();
            showNotification('تم تحميل رمز QR بنجاح', 'success');
        }

        function shareQR() {
            if (!currentQRData) return;

            if (navigator.share) {
                // استخدام Web Share API إذا كان متاحاً
                navigator.share({
                    title: 'جهة اتصال',
                    text: currentQRData,
                }).then(() => {
                    showNotification('تم مشاركة جهة الاتصال بنجاح', 'success');
                }).catch(err => {
                    console.log('خطأ في المشاركة:', err);
                    shareQRFallback();
                });
            } else {
                shareQRFallback();
            }
        }

        function shareQRFallback() {
            // نسخ البيانات إلى الحافظة
            navigator.clipboard.writeText(currentQRData).then(() => {
                showNotification('تم نسخ بيانات جهة الاتصال إلى الحافظة', 'success');
            }).catch(() => {
                // إنشاء رابط مؤقت للمشاركة
                const shareUrl = `data:text/plain;charset=utf-8,${encodeURIComponent(currentQRData)}`;
                const link = document.createElement('a');
                link.href = shareUrl;
                link.download = 'contact.txt';
                link.click();
                showNotification('تم تحميل ملف جهة الاتصال', 'info');
            });
        }

        function findDuplicateNumbers() {
            if (allContacts.length === 0) {
                showNotification('لا يوجد جهات اتصال للفحص', 'error');
                return;
            }

            // إنشاء خريطة للأرقام وجهات الاتصال المرتبطة بها
            const phoneMap = new Map();

            allContacts.forEach((contact, index) => {
                if (contact.phone && contact.phone.length > 0) {
                    contact.phone.forEach(phone => {
                        // تنظيف رقم الهاتف (إزالة المسافات والرموز)
                        const cleanPhone = phone.replace(/[\s\-\(\)\+]/g, '');

                        if (!phoneMap.has(cleanPhone)) {
                            phoneMap.set(cleanPhone, []);
                        }
                        phoneMap.get(cleanPhone).push({
                            contact: contact,
                            index: index,
                            originalPhone: phone
                        });
                    });
                }
            });

            // العثور على الأرقام المتكررة
            duplicatePhones = [];
            phoneMap.forEach((contacts, phone) => {
                if (contacts.length > 1) {
                    duplicatePhones.push({
                        phone: phone,
                        originalPhone: contacts[0].originalPhone,
                        contacts: contacts
                    });
                }
            });

            if (duplicatePhones.length === 0) {
                showNotification('لم يتم العثور على أرقام متكررة', 'info');
                return;
            }

            // عرض النتائج في modal
            displayDuplicates();
            duplicatesModal.classList.remove('hidden');
            showNotification(`تم العثور على ${duplicatePhones.length} رقم متكرر`, 'info');
        }

        function displayDuplicates() {
            duplicatesList.innerHTML = '';

            duplicatePhones.forEach((duplicate, index) => {
                const duplicateDiv = document.createElement('div');
                duplicateDiv.className = 'mb-4 p-3 border rounded bg-gray-100';

                const contactsInfo = duplicate.contacts.map(c =>
                    `<span class="text-sm bg-blue-100 px-2 py-1 rounded mr-2">${c.contact.name || 'بدون اسم'}</span>`
                ).join('');

                duplicateDiv.innerHTML = `
                    <div class="font-bold text-red-600 mb-2">📞 ${duplicate.originalPhone}</div>
                    <div class="text-sm text-gray-600 mb-2">متكرر في ${duplicate.contacts.length} جهة اتصال:</div>
                    <div class="mb-2">${contactsInfo}</div>
                    <button onclick="removeDuplicatePhone(${index})"
                            class="text-xs bg-red-500 text-white px-2 py-1 rounded">
                        حذف المتكررات (الاحتفاظ بالأول)
                    </button>
                `;

                duplicatesList.appendChild(duplicateDiv);
            });
        }

        function removeDuplicatePhone(duplicateIndex) {
            const duplicate = duplicatePhones[duplicateIndex];

            // الاحتفاظ بأول جهة اتصال وحذف الباقي
            const contactsToRemove = duplicate.contacts.slice(1);

            // حذف جهات الاتصال المتكررة من المصفوفة الرئيسية
            contactsToRemove.forEach(contactInfo => {
                const index = allContacts.findIndex(c => c === contactInfo.contact);
                if (index > -1) {
                    allContacts.splice(index, 1);
                }
            });

            // إزالة هذا المتكرر من قائمة المتكررات
            duplicatePhones.splice(duplicateIndex, 1);

            // تحديث العرض
            displayContacts(allContacts);
            displayDuplicates();

            showNotification(`تم حذف ${contactsToRemove.length} جهة اتصال متكررة`, 'success');

            // إغلاق modal إذا لم تعد هناك متكررات
            if (duplicatePhones.length === 0) {
                duplicatesModal.classList.add('hidden');
                showNotification('تم حذف جميع الأرقام المتكررة', 'success');
            }
        }

        function removeDuplicateContacts() {
            if (duplicatePhones.length === 0) {
                showNotification('لا يوجد أرقام متكررة لحذفها', 'error');
                return;
            }

            let removedCount = 0;

            // حذف جميع المتكررات (الاحتفاظ بالأول من كل مجموعة)
            duplicatePhones.forEach(duplicate => {
                const contactsToRemove = duplicate.contacts.slice(1);

                contactsToRemove.forEach(contactInfo => {
                    const index = allContacts.findIndex(c => c === contactInfo.contact);
                    if (index > -1) {
                        allContacts.splice(index, 1);
                        removedCount++;
                    }
                });
            });

            // مسح قائمة المتكررات
            duplicatePhones = [];

            // تحديث العرض وإغلاق modal
            displayContacts(allContacts);
            duplicatesModal.classList.add('hidden');

            showNotification(`تم حذف ${removedCount} جهة اتصال متكررة`, 'success');
        }

        function clearAllContacts() {
            if (allContacts.length === 0) {
                showNotification('لا يوجد جهات اتصال لمسحها', 'error');
                return;
            }

            if (confirm('هل أنت متأكد من مسح جميع جهات الاتصال؟')) {
                const count = allContacts.length;
                allContacts = [];
                duplicatePhones = [];
                lastFileContent = '';

                displayContacts(allContacts);
                localStorage.removeItem('lastVCF');

                showNotification(`تم مسح ${count} جهة اتصال`, 'success');
            }
        }

        function shareAllContacts() {
            if (allContacts.length === 0) {
                showNotification('لا يوجد جهات اتصال للمشاركة', 'error');
                return;
            }

            // إنشاء نص يحتوي على جميع جهات الاتصال
            const contactsText = allContacts.map(contact => {
                let text = `📋 ${contact.name || 'بدون اسم'}\n`;

                if (contact.phone && contact.phone.length > 0) {
                    text += contact.phone.map(p => `📞 ${p}`).join('\n') + '\n';
                }

                if (contact.email) text += `📧 ${contact.email}\n`;
                if (contact.org) text += `🏢 ${contact.org}\n`;
                if (contact.address) text += `📍 ${contact.address}\n`;
                if (contact.note) text += `📝 ${contact.note}\n`;

                return text;
            }).join('\n' + '─'.repeat(30) + '\n\n');

            const fullText = `📱 جهات الاتصال (${allContacts.length})\n\n${contactsText}`;

            if (navigator.share) {
                navigator.share({
                    title: `جهات الاتصال (${allContacts.length})`,
                    text: fullText,
                }).then(() => {
                    showNotification('تم مشاركة جهات الاتصال بنجاح', 'success');
                }).catch(err => {
                    console.log('خطأ في المشاركة:', err);
                    shareAllFallback(fullText);
                });
            } else {
                shareAllFallback(fullText);
            }
        }

        function shareAllFallback(text) {
            // محاولة نسخ النص إلى الحافظة
            navigator.clipboard.writeText(text).then(() => {
                showNotification('تم نسخ جميع جهات الاتصال إلى الحافظة', 'success');
            }).catch(() => {
                // إنشاء ملف للتحميل
                const blob = new Blob([text], { type: 'text/plain;charset=utf-8' });
                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);
                link.download = 'all-contacts.txt';
                link.click();
                showNotification('تم تحميل ملف جهات الاتصال', 'info');
            });
        }

        // جعل الوظائف متاحة عالمياً
        window.removeDuplicatePhone = removeDuplicatePhone;

        // تحميل آخر جلسة
        window.addEventListener('DOMContentLoaded', () => {
            const last = localStorage.getItem('lastVCF');
            if (last) {
                lastFileContent = last;
                allContacts = parseVCF(last);
                displayContacts(allContacts);
            }
        });
    </script>
    <link rel="manifest" href="manifest.json">
    <script>
      if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
          navigator.serviceWorker.register('service-worker.js');
        });
      }
    </script>
</body>

</html>